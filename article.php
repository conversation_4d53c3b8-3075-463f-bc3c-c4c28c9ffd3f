<?php
include 'config.php';

// جلب slug من URL
$slug = $_GET['slug'] ?? '';

if (empty($slug)) {
    header('Location: articles.php');
    exit();
}

// جلب المقال
try {
    $stmt = $pdo->prepare("SELECT * FROM articles WHERE slug = ? AND status = 'published'");
    $stmt->execute([$slug]);
    $article = $stmt->fetch();
    
    if (!$article) {
        header('Location: articles.php');
        exit();
    }
    
    // زيادة عدد المشاهدات
    $stmt = $pdo->prepare("UPDATE articles SET views = views + 1 WHERE id = ?");
    $stmt->execute([$article['id']]);
    
} catch(PDOException $e) {
    header('Location: articles.php');
    exit();
}

// جلب مقالات ذات صلة
try {
    $stmt = $pdo->prepare("SELECT * FROM articles WHERE id != ? AND status = 'published' ORDER BY RAND() LIMIT 3");
    $stmt->execute([$article['id']]);
    $related_articles = $stmt->fetchAll();
} catch(PDOException $e) {
    $related_articles = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($article['meta_title'] ?: $article['title']); ?> - شركة نقرة للتسويق الإلكتروني</title>
    <meta name="description" content="<?php echo htmlspecialchars($article['meta_description'] ?: $article['excerpt']); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($article['tags']); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="assets/images/logo2.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #2563eb;
            --accent-color: #f59e0b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --text-color: #1f2937;
            --light-bg: #f8fafc;
            --white: #ffffff;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            --gradient-accent: linear-gradient(135deg, var(--accent-color), #d97706);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            color: var(--text-color);
            line-height: 1.8;
        }

        /* Navbar */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow);
            padding: 1rem 0;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .navbar-brand img {
            height: 45px;
            margin-left: 15px;
        }

        .navbar-brand .company-name {
            display: flex;
            flex-direction: column;
            line-height: 1.2;
        }

        .navbar-brand .company-name .main-name {
            font-size: 1.1rem;
            font-weight: 800;
            color: var(--primary-color);
        }

        .navbar-brand .company-name .sub-name {
            font-size: 0.8rem;
            font-weight: 500;
            color: var(--accent-color);
        }

        .nav-link {
            font-weight: 500;
            color: var(--text-color) !important;
            margin: 0 10px;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
        }

        /* Article Header */
        .article-header {
            background: var(--gradient-primary);
            color: white;
            padding: 4rem 0 2rem;
            text-align: center;
        }

        .article-title {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .article-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 1rem;
            font-size: 1rem;
        }

        .article-meta span {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        /* Article Content */
        .article-content {
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: var(--shadow-lg);
            margin: -2rem auto 3rem;
            max-width: 800px;
            position: relative;
        }

        .article-content h2 {
            color: var(--primary-color);
            font-weight: 700;
            margin: 2.5rem 0 1.5rem;
            font-size: 2rem;
        }

        .article-content h3 {
            color: var(--text-color);
            font-weight: 600;
            margin: 2rem 0 1rem;
            font-size: 1.6rem;
        }

        .article-content h4 {
            color: var(--primary-color);
            font-weight: 600;
            margin: 1.5rem 0 1rem;
            font-size: 1.3rem;
        }

        .article-content p {
            margin-bottom: 1.5rem;
            font-size: 1.2rem;
            line-height: 1.9;
            color: var(--text-color);
        }

        .article-content ul, .article-content ol {
            margin: 1.5rem 0;
            padding-right: 2rem;
        }

        .article-content li {
            margin-bottom: 0.8rem;
            font-size: 1.2rem;
            line-height: 1.8;
            color: var(--text-color);
        }

        /* Tags */
        .article-tags {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 2px solid #e5e7eb;
        }

        .tag {
            display: inline-block;
            background: var(--gradient-accent);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 600;
            margin: 0.25rem;
            text-decoration: none;
        }

        /* Related Articles */
        .related-articles {
            padding: 4rem 0;
        }

        .related-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            height: 100%;
        }

        .related-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .related-card .card-body {
            padding: 1.5rem;
        }

        .related-card h5 {
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 1rem;
            line-height: 1.4;
        }

        .related-card p {
            color: #6b7280;
            font-size: 0.95rem;
            margin-bottom: 1rem;
        }

        .related-card .btn {
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 0.5rem 1.5rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .related-card .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        /* Back Button */
        .back-btn {
            display: inline-flex;
            align-items: center;
            background: var(--gradient-primary);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .back-btn i {
            margin-left: 10px;
        }

        /* Footer */
        .footer {
            background: linear-gradient(135deg, #1f2937, #111827);
            color: white;
            padding: 4rem 0 2rem;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--primary-color));
        }

        .footer h5 {
            color: var(--accent-color);
            margin-bottom: 1.5rem;
            font-weight: 700;
            font-size: 1.2rem;
            position: relative;
        }

        .footer h5::after {
            content: '';
            position: absolute;
            bottom: -8px;
            right: 0;
            width: 50px;
            height: 2px;
            background: var(--accent-color);
        }

        .footer a {
            color: #d1d5db;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-block;
            padding: 5px 0;
        }

        .footer a:hover {
            color: var(--accent-color);
            transform: translateX(-5px);
        }

        .footer .social-links a {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            margin-left: 10px;
            transition: all 0.3s ease;
        }

        .footer .social-links a:hover {
            background: var(--accent-color);
            transform: translateY(-3px);
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid #374151;
            margin-top: 2rem;
            padding-top: 2rem;
            text-align: center;
        }

        .footer-bottom p {
            margin: 0;
            color: #9ca3af;
        }

        /* Floating Contact Buttons */
        .floating-contact {
            position: fixed;
            right: 20px;
            bottom: 20px;
            z-index: 1050;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .floating-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 65px;
            height: 65px;
            border-radius: 50%;
            color: white;
            font-size: 1.6rem;
            text-decoration: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }

        .floating-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 50%;
            animation: ripple 2s infinite;
        }

        .whatsapp-btn {
            background: linear-gradient(135deg, #25d366, #128c7e);
        }

        .whatsapp-btn::before {
            background: rgba(37, 211, 102, 0.3);
        }

        .phone-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        }

        .phone-btn::before {
            background: rgba(37, 99, 235, 0.3);
        }

        .floating-btn:hover {
            transform: translateY(-3px) scale(1.05);
            color: white;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .floating-btn i {
            position: relative;
            z-index: 2;
        }

        @keyframes ripple {
            0% {
                transform: scale(1);
                opacity: 0.8;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.4;
            }
            100% {
                transform: scale(1.4);
                opacity: 0;
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .navbar-brand .company-name {
                display: flex;
            }

            .navbar-brand .company-name .main-name {
                font-size: 0.9rem !important;
                font-weight: 700;
            }

            .navbar-brand .company-name .sub-name {
                font-size: 0.7rem !important;
                font-weight: 400;
            }

            .navbar-brand img {
                height: 35px;
                margin-left: 10px;
            }

            .footer h5::after {
                right: 50%;
                transform: translateX(50%);
            }

            .article-title {
                font-size: 2rem;
            }

            .article-meta {
                flex-direction: column;
                gap: 1rem;
            }

            .article-content {
                padding: 2rem 1.5rem;
                margin: -1rem 1rem 2rem;
            }

            .article-content h2 {
                font-size: 1.6rem;
            }

            .article-content h3 {
                font-size: 1.4rem;
            }

            .article-content h4 {
                font-size: 1.2rem;
            }

            .article-content p {
                font-size: 1.1rem;
            }

            .article-content li {
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="assets/images/logo2.png" alt="شركة نقرة للتسويق الإلكتروني">
                <div class="company-name">
                    <span class="main-name">شركة نقرة للتسويق الإلكتروني</span>
                    <span class="sub-name">Nakra Digital Marketing</span>
                </div>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.php">خدماتنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="projects.php">أعمالنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.php">تواصل معنا</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="index.php#pricing">الخطط</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="articles.php">المقالات</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Article Header -->
    <section class="article-header" style="margin-top: 76px;">
        <div class="container">
            <h1 class="article-title" data-aos="fade-up"><?php echo htmlspecialchars($article['title']); ?></h1>
            <div class="article-meta" data-aos="fade-up" data-aos-delay="200">
                <span><i class="fas fa-calendar"></i> <?php echo date('d M Y', strtotime($article['created_at'])); ?></span>
                <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($article['author']); ?></span>
                <span><i class="fas fa-eye"></i> <?php echo number_format($article['views']); ?> مشاهدة</span>
            </div>
        </div>
    </section>

    <!-- Article Content -->
    <section class="py-5">
        <div class="container">
            <a href="articles.php" class="back-btn" data-aos="fade-right">
                <i class="fas fa-arrow-right"></i>
                العودة للمقالات
            </a>
            
            <div class="article-content" data-aos="fade-up">
                <?php if (!empty($article['image'])): ?>
                <div class="article-main-image" style="margin-bottom: 2rem; text-align: center;">
                    <img src="<?php echo htmlspecialchars($article['image']); ?>"
                         alt="<?php echo htmlspecialchars($article['title']); ?>"
                         style="width: 100%; max-width: 600px; height: auto; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                </div>
                <?php endif; ?>

                <?php echo $article['content']; ?>
                
                <?php if ($article['tags']): ?>
                <div class="article-tags">
                    <h4 style="margin-bottom: 1rem; color: var(--primary-color);">
                        <i class="fas fa-tags me-2"></i>الكلمات المفتاحية
                    </h4>
                    <?php
                    $tags = explode(',', $article['tags']);
                    foreach ($tags as $tag):
                        $tag = trim($tag);
                        if (!empty($tag)):
                    ?>
                    <span class="tag"><?php echo htmlspecialchars($tag); ?></span>
                    <?php
                        endif;
                    endforeach;
                    ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Related Articles -->
    <?php if ($related_articles): ?>
    <section class="related-articles bg-light">
        <div class="container">
            <h2 class="text-center mb-5" data-aos="fade-up">
                <i class="fas fa-newspaper me-2"></i>مقالات ذات صلة
            </h2>
            <div class="row">
                <?php foreach ($related_articles as $index => $related): ?>
                <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="<?php echo ($index + 1) * 100; ?>">
                    <div class="related-card">
                        <div class="card-body">
                            <h5><?php echo htmlspecialchars($related['title']); ?></h5>
                            <p><?php echo htmlspecialchars(substr($related['excerpt'], 0, 100)) . '...'; ?></p>
                            <a href="article.php?slug=<?php echo $related['slug']; ?>" class="btn">
                                <i class="fas fa-arrow-left me-2"></i>اقرأ المزيد
                            </a>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <!-- معلومات الشركة -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5>شركة نقرة للتسويق الإلكتروني</h5>
                    <p>شركة متخصصة في التسويق الإلكتروني والاستضافة والإعلانات الممولة منذ 2018. نساعد الشركات على النمو في العالم الرقمي.</p>
                    <div class="social-links mt-3">
                        <a href="#" title="فيسبوك"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" title="تويتر"><i class="fab fa-twitter"></i></a>
                        <a href="#" title="إنستجرام"><i class="fab fa-instagram"></i></a>
                        <a href="#" title="لينكد إن"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>

                <!-- روابط سريعة -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="index.php"><i class="fas fa-chevron-left me-2"></i>الرئيسية</a></li>
                        <li><a href="services.php"><i class="fas fa-chevron-left me-2"></i>خدماتنا</a></li>
                        <li><a href="projects.php"><i class="fas fa-chevron-left me-2"></i>أعمالنا</a></li>
                        <li><a href="index.php#pricing"><i class="fas fa-chevron-left me-2"></i>الخطط</a></li>
                        <li><a href="contact.php"><i class="fas fa-chevron-left me-2"></i>تواصل معنا</a></li>
                        <li><a href="articles.php"><i class="fas fa-chevron-left me-2"></i>المقالات</a></li>
                    </ul>
                </div>

                <!-- أحدث المقالات -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5>أحدث المقالات</h5>
                    <ul class="list-unstyled">
                        <li><a href="articles.php"><i class="fas fa-chevron-left me-2"></i>تحسين محركات البحث</a></li>
                        <li><a href="articles.php"><i class="fas fa-chevron-left me-2"></i>إعلانات جوجل الفعالة</a></li>
                        <li><a href="articles.php"><i class="fas fa-chevron-left me-2"></i>تصميم المواقع الحديثة</a></li>
                        <li><a href="articles.php"><i class="fas fa-chevron-left me-2"></i>التسويق الرقمي</a></li>
                        <li><a href="articles.php"><i class="fas fa-chevron-left me-2"></i>عرض جميع المقالات</a></li>
                    </ul>
                </div>

                <!-- معلومات التواصل -->
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5>معلومات التواصل</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-phone me-2 text-warning"></i>
                            <a href="tel:<?php echo COMPANY_PHONE; ?>"><?php echo COMPANY_PHONE; ?></a>
                        </li>
                        <li class="mb-2">
                            <i class="fab fa-whatsapp me-2 text-success"></i>
                            <a href="https://wa.me/<?php echo WHATSAPP_NUMBER; ?>">واتساب</a>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-envelope me-2 text-info"></i>
                            <a href="mailto:<?php echo COMPANY_EMAIL; ?>"><?php echo COMPANY_EMAIL; ?></a>
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-map-marker-alt me-2 text-danger"></i>
                            <?php echo COMPANY_ADDRESS; ?>
                        </li>
                    </ul>
                </div>
            </div>

            <hr class="my-4">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2025 شركة نقرة للتسويق الإلكتروني. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">تم التطوير بواسطة <strong>فريق نقرة</strong></p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Floating Contact Buttons -->
    <div class="floating-contact">
        <a href="https://wa.me/<?php echo WHATSAPP_NUMBER; ?>" target="_blank" class="floating-btn whatsapp-btn" title="واتساب">
            <i class="fab fa-whatsapp"></i>
        </a>
        <a href="tel:<?php echo COMPANY_PHONE; ?>" class="floating-btn phone-btn" title="اتصل بنا">
            <i class="fas fa-phone"></i>
        </a>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        AOS.init({
            duration: 1000,
            once: true
        });
    </script>
</body>
</html>
