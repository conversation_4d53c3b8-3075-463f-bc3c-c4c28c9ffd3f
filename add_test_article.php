<?php
include 'config.php';

try {
    $stmt = $pdo->prepare('INSERT INTO articles (title, slug, excerpt, content, author, status, featured, meta_title, meta_description, tags) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)');
    $stmt->execute([
        'دليل شامل لتحسين محركات البحث SEO',
        'seo-guide-2025',
        'تعلم كيفية تحسين موقعك لمحركات البحث وزيادة الزيارات المجانية من جوجل',
        '<h2>مقدمة عن تحسين محركات البحث</h2>
<p>تحسين محركات البحث (SEO) هو عملية تحسين موقعك الإلكتروني ليظهر في النتائج الأولى لمحركات البحث مثل جوجل.</p>

<h3>أهمية تحسين محركات البحث</h3>
<ul>
<li>زيادة الزيارات المجانية للموقع</li>
<li>تحسين ترتيب الموقع في نتائج البحث</li>
<li>زيادة الثقة والمصداقية</li>
<li>تحسين تجربة المستخدم</li>
</ul>

<h3>خطوات تحسين محركات البحث</h3>
<p>هناك عدة خطوات مهمة لتحسين موقعك:</p>

<h4>1. البحث عن الكلمات المفتاحية</h4>
<p>ابحث عن الكلمات التي يستخدمها جمهورك المستهدف في البحث.</p>

<h4>2. تحسين المحتوى</h4>
<p>اكتب محتوى عالي الجودة يجيب على أسئلة زوارك.</p>

<h4>3. تحسين العناوين والوصف</h4>
<p>استخدم عناوين جذابة ووصف مناسب لكل صفحة.</p>

<p>للحصول على خدمات تحسين محركات البحث الاحترافية، تواصل مع فريق شركة نقرة للتسويق الإلكتروني.</p>',
        'فريق نقرة',
        'published',
        1,
        'دليل شامل لتحسين محركات البحث SEO 2025',
        'تعلم كيفية تحسين موقعك لمحركات البحث وزيادة الزيارات المجانية من جوجل مع دليلنا الشامل',
        'SEO, تحسين محركات البحث, جوجل, تسويق إلكتروني, مواقع'
    ]);
    echo 'تم إضافة المقال التجريبي بنجاح!<br>';
    echo '<a href="articles.php">عرض المقالات</a><br>';
    echo '<a href="admin/articles.php">إدارة المقالات</a>';
} catch(Exception $e) {
    echo 'خطأ: ' . $e->getMessage();
}
?>
