<?php
// ملف اختبار الاتصال بقاعدة البيانات والمقالات
include 'config.php';

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";

try {
    // اختبار الاتصال
    $stmt = $pdo->query("SELECT 1");
    echo "<p style='color: green;'>✓ الاتصال بقاعدة البيانات يعمل بشكل صحيح</p>";
    
    // اختبار جدول المقالات
    $stmt = $pdo->query("SHOW TABLES LIKE 'articles'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ جدول المقالات موجود</p>";
        
        // عرض عدد المقالات
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM articles");
        $result = $stmt->fetch();
        echo "<p>عدد المقالات الإجمالي: " . $result['total'] . "</p>";
        
        // عرض المقالات المنشورة
        $stmt = $pdo->query("SELECT COUNT(*) as published FROM articles WHERE status = 'published'");
        $result = $stmt->fetch();
        echo "<p>عدد المقالات المنشورة: " . $result['published'] . "</p>";
        
        // عرض المقالات في المسودة
        $stmt = $pdo->query("SELECT COUNT(*) as draft FROM articles WHERE status = 'draft'");
        $result = $stmt->fetch();
        echo "<p>عدد المقالات في المسودة: " . $result['draft'] . "</p>";
        
        // عرض آخر 5 مقالات
        echo "<h3>آخر 5 مقالات:</h3>";
        $stmt = $pdo->query("SELECT id, title, status, created_at FROM articles ORDER BY created_at DESC LIMIT 5");
        $articles = $stmt->fetchAll();
        
        if ($articles) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>العنوان</th><th>الحالة</th><th>تاريخ الإنشاء</th></tr>";
            foreach ($articles as $article) {
                echo "<tr>";
                echo "<td>" . $article['id'] . "</td>";
                echo "<td>" . htmlspecialchars($article['title']) . "</td>";
                echo "<td>" . ($article['status'] == 'published' ? 'منشور' : 'مسودة') . "</td>";
                echo "<td>" . $article['created_at'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>لا توجد مقالات</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ جدول المقالات غير موجود</p>";
    }
    
    // اختبار جدول الإدارة
    $stmt = $pdo->query("SHOW TABLES LIKE 'admin'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ جدول الإدارة موجود</p>";
        
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM admin");
        $result = $stmt->fetch();
        echo "<p>عدد المديرين: " . $result['total'] . "</p>";
    } else {
        echo "<p style='color: red;'>✗ جدول الإدارة غير موجود</p>";
    }
    
} catch(PDOException $e) {
    echo "<p style='color: red;'>✗ خطأ في الاتصال: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>العودة للموقع الرئيسي</a> | <a href='admin/login.php'>لوحة التحكم</a></p>";
?>
