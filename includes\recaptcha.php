<?php
/**
 * ملف مساعد للتعامل مع Google reCAPTCHA
 */

/**
 * جلب إعدادات reCAPTCHA من قاعدة البيانات
 */
function getRecaptchaSettings($pdo) {
    $settings = [
        'site_key' => '',
        'secret_key' => '',
        'enabled' => false
    ];
    
    try {
        $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('recaptcha_site_key', 'recaptcha_secret_key', 'recaptcha_enabled')");
        $db_settings = $stmt->fetchAll();
        
        foreach ($db_settings as $setting) {
            switch ($setting['setting_key']) {
                case 'recaptcha_site_key':
                    $settings['site_key'] = $setting['setting_value'];
                    break;
                case 'recaptcha_secret_key':
                    $settings['secret_key'] = $setting['setting_value'];
                    break;
                case 'recaptcha_enabled':
                    $settings['enabled'] = (bool)$setting['setting_value'];
                    break;
            }
        }
    } catch(PDOException $e) {
        // في حالة عدم وجود الجدول أو خطأ
    }
    
    return $settings;
}

/**
 * التحقق من صحة reCAPTCHA
 */
function verifyRecaptcha($recaptcha_response, $secret_key) {
    if (empty($recaptcha_response) || empty($secret_key)) {
        return false;
    }
    
    $url = 'https://www.google.com/recaptcha/api/siteverify';
    $data = [
        'secret' => $secret_key,
        'response' => $recaptcha_response,
        'remoteip' => $_SERVER['REMOTE_ADDR'] ?? ''
    ];
    
    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    if ($result === false) {
        return false;
    }
    
    $response = json_decode($result, true);
    return isset($response['success']) && $response['success'] === true;
}

/**
 * عرض reCAPTCHA في النموذج
 */
function renderRecaptcha($site_key, $enabled = true) {
    if (!$enabled || empty($site_key)) {
        return '';
    }
    
    return '
    <div class="mb-3">
        <div class="g-recaptcha" data-sitekey="' . htmlspecialchars($site_key) . '"></div>
    </div>';
}

/**
 * إضافة سكريبت reCAPTCHA
 */
function getRecaptchaScript($enabled = true) {
    if (!$enabled) {
        return '';
    }
    
    return '<script src="https://www.google.com/recaptcha/api.js" async defer></script>';
}

/**
 * التحقق من reCAPTCHA في النموذج
 */
function validateRecaptchaInForm($pdo) {
    $recaptcha_settings = getRecaptchaSettings($pdo);
    
    // إذا كان reCAPTCHA غير مفعل، السماح بالمرور
    if (!$recaptcha_settings['enabled']) {
        return true;
    }
    
    // إذا كان مفعل ولكن لا توجد مفاتيح، رفض
    if (empty($recaptcha_settings['secret_key'])) {
        return false;
    }
    
    // التحقق من الاستجابة
    $recaptcha_response = $_POST['g-recaptcha-response'] ?? '';
    return verifyRecaptcha($recaptcha_response, $recaptcha_settings['secret_key']);
}
?>
