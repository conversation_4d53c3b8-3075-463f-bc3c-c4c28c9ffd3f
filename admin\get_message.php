<?php
session_start();
include '../config.php';

// تحديد نوع المحتوى
header('Content-Type: application/json; charset=utf-8');

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

$id = $_GET['id'] ?? 0;

if (!$id) {
    echo json_encode(['success' => false, 'message' => 'معرف الرسالة مطلوب']);
    exit;
}

try {
    $stmt = $pdo->prepare("SELECT * FROM contact_messages WHERE id = ?");
    $stmt->execute([$id]);
    $message = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($message) {
        echo json_encode([
            'success' => true,
            'message' => $message
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'الرسالة غير موجودة',
            'debug' => 'Message ID: ' . $id
        ], JSON_UNESCAPED_UNICODE);
    }
} catch(PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage(),
        'debug' => 'PDO Error'
    ], JSON_UNESCAPED_UNICODE);
}
?>
