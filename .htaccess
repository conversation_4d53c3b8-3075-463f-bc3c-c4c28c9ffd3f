# Apache Configuration for Nakra Digital Marketing Website
# ملف تكوين Apache لموقع شركة نقرة للتسويق الإلكتروني

# Enable Rewrite Engine
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff
    
    # Prevent clickjacking
    Header always set X-Frame-Options DENY
    
    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (adjust as needed)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self';"
</IfModule>

# Hide sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "(\.sql|\.log|\.md|composer\.(json|lock)|package\.json)$">
    Order allow,deny
    Deny from all
</Files>

# Protect config directory
<IfModule mod_rewrite.c>
    RewriteRule ^config/ - [F,L]
    RewriteRule ^includes/ - [F,L]
    RewriteRule ^languages/ - [F,L]
</IfModule>

# Prevent access to PHP files in uploads directory
<IfModule mod_rewrite.c>
    RewriteRule ^uploads/.*\.php$ - [F,L]
</IfModule>

# URL Rewriting for clean URLs
<IfModule mod_rewrite.c>
    # Remove index.php from URLs
    RewriteCond %{THE_REQUEST} /index\.php[?\s] [NC]
    RewriteRule ^index\.php$ / [R=301,L]
    
    # Project details page
    RewriteRule ^project/([0-9]+)/?$ project.php?id=$1 [L,QSA]
    RewriteRule ^project/([0-9]+)/([a-z]{2})/?$ project.php?id=$1&lang=$2 [L,QSA]
    
    # Projects page
    RewriteRule ^projects/?$ projects.php [L,QSA]
    RewriteRule ^projects/([a-z]{2})/?$ projects.php?lang=$1 [L,QSA]
    
    # Blog/Articles
    RewriteRule ^blog/?$ blog.php [L,QSA]
    RewriteRule ^blog/([a-z]{2})/?$ blog.php?lang=$1 [L,QSA]
    RewriteRule ^article/([a-zA-Z0-9\-]+)/?$ article.php?slug=$1 [L,QSA]
    RewriteRule ^article/([a-zA-Z0-9\-]+)/([a-z]{2})/?$ article.php?slug=$1&lang=$2 [L,QSA]
    
    # Language switching
    RewriteRule ^ar/?$ index.php?lang=ar [L,QSA]
    RewriteRule ^en/?$ index.php?lang=en [L,QSA]
</IfModule>

# Compression
<IfModule mod_deflate.c>
    # Compress HTML, CSS, JavaScript, Text, XML and fonts
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
</IfModule>

# Browser Caching
<IfModule mod_expires.c>
    ExpiresActive on
    
    # Images
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    
    # Fonts
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# Cache Control Headers
<IfModule mod_headers.c>
    # Cache static assets
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|webp|svg|woff|woff2|ttf|otf)$">
        Header set Cache-Control "public, max-age=2592000"
    </FilesMatch>
    
    # Don't cache HTML files
    <FilesMatch "\.(html|htm|php)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires 0
    </FilesMatch>
</IfModule>

# Error Pages
ErrorDocument 404 /404.php
ErrorDocument 403 /403.php
ErrorDocument 500 /500.php

# Prevent hotlinking
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?nakraformarketing\.com [NC]
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?google\. [NC]
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?bing\. [NC]
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?yahoo\. [NC]
    RewriteRule \.(jpg|jpeg|png|gif|webp|svg)$ - [F]
</IfModule>

# Limit file upload size (adjust as needed)
php_value upload_max_filesize 10M
php_value post_max_size 10M
php_value max_execution_time 300
php_value max_input_vars 3000

# Hide PHP version
<IfModule mod_headers.c>
    Header unset X-Powered-By
    Header always unset X-Powered-By
</IfModule>

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Force HTTPS (uncomment if you have SSL certificate)
# <IfModule mod_rewrite.c>
#     RewriteCond %{HTTPS} off
#     RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
# </IfModule>

# Prevent directory browsing
Options -Indexes

# Default charset
AddDefaultCharset UTF-8

# MIME types for web fonts
AddType application/font-woff2 .woff2
AddType application/font-woff .woff
AddType application/vnd.ms-fontobject .eot
AddType font/ttf .ttf
AddType font/otf .otf
