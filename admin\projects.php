<?php
session_start();
include '../config.php';

if (!isLoggedIn()) {
    redirectToLogin();
}

$message = '';
$error = '';

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $title = cleanInput($_POST['title']);
                $description = cleanInput($_POST['description']);
                $url = cleanInput($_POST['url']);
                $image = '';

                // التحقق من الصورة المختارة أولاً
                if (!empty($_POST['selected_image'])) {
                    $image = cleanInput($_POST['selected_image']);
                }
                // إذا لم تكن هناك صورة مختارة، تحقق من رفع صورة جديدة
                elseif (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                    $uploadDir = '../assets/images/projects/';
                    if (!file_exists($uploadDir)) {
                        mkdir($uploadDir, 0755, true);
                    }

                    $fileExtension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
                    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

                    if (in_array($fileExtension, $allowedExtensions)) {
                        // التحقق من حجم الملف (أقل من 5MB)
                        if ($_FILES['image']['size'] <= 5 * 1024 * 1024) {
                            $fileName = 'project_' . time() . '_' . rand(1000, 9999) . '.' . $fileExtension;
                            $uploadPath = $uploadDir . $fileName;

                            if (move_uploaded_file($_FILES['image']['tmp_name'], $uploadPath)) {
                                $image = 'assets/images/projects/' . $fileName;
                            } else {
                                $error = 'فشل في رفع الصورة';
                            }
                        } else {
                            $error = 'حجم الصورة كبير جداً (الحد الأقصى 5MB)';
                        }
                    } else {
                        $error = 'نوع الملف غير مدعوم. استخدم: jpg, jpeg, png, gif, webp';
                    }
                }

                if (!empty($title) && !empty($description)) {
                    try {
                        $stmt = $pdo->prepare("INSERT INTO projects (title, description, url, image) VALUES (?, ?, ?, ?)");
                        $stmt->execute([$title, $description, $url, $image]);
                        $message = 'تم إضافة المشروع بنجاح';
                    } catch(PDOException $e) {
                        $error = 'حدث خطأ أثناء إضافة المشروع';
                    }
                } else {
                    $error = 'العنوان والوصف مطلوبان';
                }
                break;

            case 'edit':
                $id = (int)$_POST['id'];
                $title = cleanInput($_POST['title']);
                $description = cleanInput($_POST['description']);
                $url = cleanInput($_POST['url']);
                $image = $_POST['current_image'] ?? '';

                // التحقق من الصورة المختارة أولاً
                if (!empty($_POST['selected_image'])) {
                    $image = cleanInput($_POST['selected_image']);
                }
                // إذا لم تكن هناك صورة مختارة، تحقق من رفع صورة جديدة
                elseif (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                    $uploadDir = '../assets/images/projects/';
                    if (!file_exists($uploadDir)) {
                        mkdir($uploadDir, 0755, true);
                    }

                    $fileExtension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
                    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

                    if (in_array($fileExtension, $allowedExtensions)) {
                        // التحقق من حجم الملف (أقل من 5MB)
                        if ($_FILES['image']['size'] <= 5 * 1024 * 1024) {
                            // حذف الصورة القديمة إذا كانت مختلفة
                            $oldImage = $_POST['current_image'] ?? '';
                            if (!empty($oldImage) && file_exists('../' . $oldImage)) {
                                unlink('../' . $oldImage);
                            }

                            $fileName = 'project_' . time() . '_' . rand(1000, 9999) . '.' . $fileExtension;
                            $uploadPath = $uploadDir . $fileName;

                            if (move_uploaded_file($_FILES['image']['tmp_name'], $uploadPath)) {
                                $image = 'assets/images/projects/' . $fileName;
                            } else {
                                $error = 'فشل في رفع الصورة';
                            }
                        } else {
                            $error = 'حجم الصورة كبير جداً (الحد الأقصى 5MB)';
                        }
                    } else {
                        $error = 'نوع الملف غير مدعوم. استخدم: jpg, jpeg, png, gif, webp';
                    }
                }

                if (!empty($title) && !empty($description)) {
                    try {
                        $stmt = $pdo->prepare("UPDATE projects SET title = ?, description = ?, url = ?, image = ? WHERE id = ?");
                        $stmt->execute([$title, $description, $url, $image, $id]);
                        $message = 'تم تحديث المشروع بنجاح';
                    } catch(PDOException $e) {
                        $error = 'حدث خطأ أثناء تحديث المشروع';
                    }
                } else {
                    $error = 'العنوان والوصف مطلوبان';
                }
                break;
                
            case 'delete':
                $id = (int)$_POST['id'];
                try {
                    // جلب معلومات المشروع لحذف الصورة
                    $stmt = $pdo->prepare("SELECT image FROM projects WHERE id = ?");
                    $stmt->execute([$id]);
                    $project = $stmt->fetch();

                    // حذف الصورة إذا كانت موجودة
                    if ($project && !empty($project['image']) && file_exists('../' . $project['image'])) {
                        unlink('../' . $project['image']);
                    }

                    // حذف المشروع من قاعدة البيانات
                    $stmt = $pdo->prepare("DELETE FROM projects WHERE id = ?");
                    $stmt->execute([$id]);
                    $message = 'تم حذف المشروع بنجاح';
                } catch(PDOException $e) {
                    $error = 'حدث خطأ أثناء حذف المشروع';
                }
                break;
        }
    }
}

// جلب المشاريع
try {
    $stmt = $pdo->query("SELECT * FROM projects ORDER BY created_at DESC");
    $projects = $stmt->fetchAll();
} catch(PDOException $e) {
    $projects = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المشاريع - لوحة التحكم</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/logo2.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/logo2.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">

    <?php include 'includes/admin-styles.php'; ?>

    <style>


        .sidebar {
            background: var(--gradient-primary);
            min-height: 100vh;
            width: 280px;
            position: fixed;
            top: 0;
            right: 0;
            z-index: 1000;
            padding: 0;
            box-shadow: var(--shadow-lg);
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .sidebar-brand {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            background: rgba(255, 255, 255, 0.9);
            padding: 1rem;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar-brand img {
            width: 50px;
            height: 50px;
            margin-left: 15px;
        }

        .sidebar-brand h4 {
            color: var(--primary-color);
            font-weight: 800;
            font-size: 1.2rem;
            margin: 0;
        }

        .admin-info {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .sidebar a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            padding: 1rem 1.5rem;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            border-right: 4px solid transparent;
        }

        .sidebar a:hover,
        .sidebar a.active {
            color: white;
            background: rgba(255, 255, 255, 0.1);
            border-right-color: var(--accent-color);
        }

        .sidebar a i {
            margin-left: 15px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            margin-right: 280px;
            padding: 2rem;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            padding: 1.5rem 2rem;
            border-radius: 20px;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .page-header h2 {
            margin: 0;
            color: var(--text-color);
            font-weight: 800;
            font-size: 1.8rem;
        }

        .card {
            border: none;
            border-radius: 20px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .card-header {
            background: var(--gradient-primary);
            color: white;
            border-radius: 20px 20px 0 0 !important;
            padding: 1.5rem;
            border: none;
        }

        .card-header h5 {
            margin: 0;
            font-weight: 700;
        }

        .card-body {
            padding: 2rem;
        }

        .btn {
            border-radius: 50px;
            font-weight: 600;
            padding: 0.5rem 1.5rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .alert {
            border-radius: 15px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }

        .table {
            border-radius: 15px;
            overflow: hidden;
        }

        .table th {
            background: var(--light-bg);
            border: none;
            font-weight: 700;
            color: var(--text-color);
        }

        .table td {
            border: none;
            vertical-align: middle;
        }

        .modal-content {
            border-radius: 20px;
            border: none;
        }

        .modal-header {
            background: var(--gradient-primary);
            color: white;
            border-radius: 20px 20px 0 0;
        }
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .btn {
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <div class="main-content">
                <div class="page-header">
                    <div style="display: flex; align-items: center;">
                        <img src="../assets/images/logo2.png" alt="Logo" style="width: 40px; height: 40px; margin-left: 15px;">
                        <h2>إدارة المشاريع</h2>
                    </div>
                </div>
                
                <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>
                
                <!-- إضافة مشروع جديد -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>إضافة مشروع جديد</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="add">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="title" class="form-label">عنوان المشروع</label>
                                    <input type="text" class="form-control" id="title" name="title" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="url" class="form-label">رابط المشروع (اختياري)</label>
                                    <input type="url" class="form-control" id="url" name="url">
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="description" class="form-label">وصف المشروع</label>
                                <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="image" class="form-label">صورة المشروع (اختياري)</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                        <div class="form-text">رفع صورة جديدة</div>
                                    </div>
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-outline-primary w-100" onclick="openImageSelector('add')">
                                            <i class="fas fa-images me-2"></i>اختر من الصور الموجودة
                                        </button>
                                    </div>
                                </div>
                                <input type="hidden" id="selected_image" name="selected_image">
                                <div id="selected_image_preview" class="mt-2"></div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i> إضافة المشروع
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- قائمة المشاريع -->
                <div class="card">
                    <div class="card-header">
                        <h5>المشاريع الحالية</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($projects): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الصورة</th>
                                        <th>العنوان</th>
                                        <th>الوصف</th>
                                        <th>الرابط</th>
                                        <th>تاريخ الإضافة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($projects as $project): ?>
                                    <tr>
                                        <td>
                                            <?php if (!empty($project['image'])): ?>
                                                <img src="../<?php echo htmlspecialchars($project['image']); ?>"
                                                     alt="<?php echo htmlspecialchars($project['title']); ?>"
                                                     style="width: 60px; height: 60px; object-fit: cover; border-radius: 8px;">
                                            <?php else: ?>
                                                <div style="width: 60px; height: 60px; background: #f3f4f6; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($project['title']); ?></td>
                                        <td><?php echo htmlspecialchars(substr($project['description'], 0, 80)) . '...'; ?></td>
                                        <td>
                                            <?php if ($project['url']): ?>
                                            <a href="<?php echo htmlspecialchars($project['url']); ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-external-link-alt"></i> عرض
                                            </a>
                                            <?php else: ?>
                                            -
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('Y-m-d', strtotime($project['created_at'])); ?></td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-warning me-1" onclick="editProject(<?php echo $project['id']; ?>)">
                                                <i class="fas fa-edit"></i> تعديل
                                            </button>
                                            <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا المشروع؟')">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="id" value="<?php echo $project['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i> حذف
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                            <p class="text-muted">لا توجد مشاريع حالياً</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

    <!-- Edit Project Modal -->
    <div class="modal fade" id="editProjectModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل المشروع</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data" id="editProjectForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="id" id="edit_project_id">
                        <input type="hidden" name="current_image" id="edit_current_image">

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_title" class="form-label">عنوان المشروع</label>
                                <input type="text" class="form-control" id="edit_title" name="title" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="edit_url" class="form-label">رابط المشروع (اختياري)</label>
                                <input type="url" class="form-control" id="edit_url" name="url">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_description" class="form-label">وصف المشروع</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="edit_image" class="form-label">صورة المشروع (اختياري)</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="file" class="form-control" id="edit_image" name="image" accept="image/*">
                                    <div class="form-text">رفع صورة جديدة</div>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-outline-primary w-100" onclick="openImageSelector('edit')">
                                        <i class="fas fa-images me-2"></i>اختر من الصور الموجودة
                                    </button>
                                </div>
                            </div>
                            <input type="hidden" id="edit_selected_image" name="selected_image">
                            <div id="current_image_preview" class="mt-2"></div>
                            <div id="edit_selected_image_preview" class="mt-2"></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i> حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Image Selector Modal -->
    <div class="modal fade" id="imageSelectorModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">اختر صورة من الصور الموجودة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row" id="imageGrid">
                        <div class="col-12 text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="selectImageBtn" disabled>اختيار الصورة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // بيانات المشاريع لجافا سكريبت
        const projects = <?php echo json_encode($projects); ?>;

        function editProject(projectId) {
            const project = projects.find(p => p.id == projectId);
            if (!project) return;

            // ملء النموذج بالبيانات
            document.getElementById('edit_project_id').value = project.id;
            document.getElementById('edit_title').value = project.title;
            document.getElementById('edit_description').value = project.description;
            document.getElementById('edit_url').value = project.url || '';
            document.getElementById('edit_current_image').value = project.image || '';

            // عرض الصورة الحالية
            const imagePreview = document.getElementById('current_image_preview');
            if (project.image) {
                imagePreview.innerHTML = `
                    <div class="current-image">
                        <p class="mb-2">الصورة الحالية:</p>
                        <img src="../${project.image}" alt="${project.title}" style="max-width: 200px; height: auto; border-radius: 8px;">
                    </div>
                `;
            } else {
                imagePreview.innerHTML = '<p class="text-muted">لا توجد صورة حالية</p>';
            }

            // عرض النافذة المنبثقة
            const modal = new bootstrap.Modal(document.getElementById('editProjectModal'));
            modal.show();
        }

        // نظام اختيار الصور
        let currentImageSelector = 'add';
        let selectedImagePath = '';

        function openImageSelector(type) {
            currentImageSelector = type;
            selectedImagePath = '';
            document.getElementById('selectImageBtn').disabled = true;

            // فتح الـ modal
            const modal = new bootstrap.Modal(document.getElementById('imageSelectorModal'));
            modal.show();

            // جلب الصور
            loadImages();
        }

        function loadImages() {
            fetch('get_images.php?type=projects')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayImages(data.images);
                    } else {
                        document.getElementById('imageGrid').innerHTML = '<div class="col-12 text-center text-danger">خطأ في جلب الصور</div>';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('imageGrid').innerHTML = '<div class="col-12 text-center text-danger">خطأ في الاتصال بالخادم</div>';
                });
        }

        function displayImages(images) {
            const imageGrid = document.getElementById('imageGrid');

            if (images.length === 0) {
                imageGrid.innerHTML = '<div class="col-12 text-center text-muted">لا توجد صور متاحة</div>';
                return;
            }

            let html = '';
            images.forEach(image => {
                html += `
                    <div class="col-md-3 col-sm-4 col-6 mb-3">
                        <div class="image-item" onclick="selectImage('${image.path}', this)" style="cursor: pointer; border: 2px solid transparent; border-radius: 8px; padding: 5px; transition: all 0.3s ease;">
                            <img src="../${image.path}" alt="${image.name}" style="width: 100%; height: 120px; object-fit: cover; border-radius: 5px;">
                            <div class="text-center mt-1">
                                <small class="text-muted">${image.name}</small>
                            </div>
                        </div>
                    </div>
                `;
            });

            imageGrid.innerHTML = html;
        }

        function selectImage(imagePath, element) {
            // إزالة التحديد من جميع الصور
            document.querySelectorAll('.image-item').forEach(item => {
                item.style.borderColor = 'transparent';
                item.style.backgroundColor = 'transparent';
            });

            // تحديد الصورة المختارة
            element.style.borderColor = '#0d6efd';
            element.style.backgroundColor = 'rgba(13, 110, 253, 0.1)';

            selectedImagePath = imagePath;
            document.getElementById('selectImageBtn').disabled = false;
        }

        // عند النقر على زر اختيار الصورة
        document.getElementById('selectImageBtn').addEventListener('click', function() {
            if (selectedImagePath) {
                if (currentImageSelector === 'add') {
                    document.getElementById('selected_image').value = selectedImagePath;
                    document.getElementById('selected_image_preview').innerHTML =
                        '<div class="mt-2"><small>الصورة المختارة:</small><br><img src="../' + selectedImagePath + '" style="max-width: 100px; height: auto; border-radius: 5px;"></div>';
                } else if (currentImageSelector === 'edit') {
                    document.getElementById('edit_selected_image').value = selectedImagePath;
                    document.getElementById('edit_selected_image_preview').innerHTML =
                        '<div class="mt-2"><small>الصورة المختارة:</small><br><img src="../' + selectedImagePath + '" style="max-width: 100px; height: auto; border-radius: 5px;"></div>';
                }

                // إغلاق الـ modal
                bootstrap.Modal.getInstance(document.getElementById('imageSelectorModal')).hide();
            }
        });
    </script>
</body>
</html>
