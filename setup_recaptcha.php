<?php
include 'config.php';

echo "<h2>إعد<PERSON> reCAPTCHA</h2>";

try {
    // إنشاء جدول الإعدادات إذا لم يكن موجوداً
    $pdo->exec("CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(255) UNIQUE NOT NULL,
        setting_value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    echo "<p style='color: green;'>✅ تم إنشاء جدول الإعدادات بنجاح</p>";
    
    // التحقق من وجود إعدادات reCAPTCHA
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE 'recaptcha_%'");
    $existing_settings = $stmt->fetchAll();
    
    echo "<h3>الإعدادات الحالية:</h3>";
    if (empty($existing_settings)) {
        echo "<p style='color: orange;'>⚠️ لا توجد إعدادات reCAPTCHA</p>";
        
        // إضافة إعدادات افتراضية
        $default_settings = [
            ['recaptcha_site_key', ''],
            ['recaptcha_secret_key', ''],
            ['recaptcha_enabled', '0']
        ];
        
        foreach ($default_settings as $setting) {
            $stmt = $pdo->prepare("INSERT IGNORE INTO settings (setting_key, setting_value) VALUES (?, ?)");
            $stmt->execute($setting);
        }
        
        echo "<p style='color: blue;'>ℹ️ تم إضافة إعدادات افتراضية</p>";
    } else {
        echo "<ul>";
        foreach ($existing_settings as $setting) {
            $value = $setting['setting_value'];
            if ($setting['setting_key'] === 'recaptcha_secret_key' && !empty($value)) {
                $value = str_repeat('*', strlen($value));
            }
            echo "<li><strong>{$setting['setting_key']}:</strong> " . ($value ?: 'فارغ') . "</li>";
        }
        echo "</ul>";
    }
    
    echo "<hr>";
    echo "<h3>خطوات تفعيل reCAPTCHA:</h3>";
    echo "<ol>";
    echo "<li>اذهب إلى <a href='https://www.google.com/recaptcha/admin' target='_blank'>Google reCAPTCHA</a></li>";
    echo "<li>أنشئ موقع جديد واختر reCAPTCHA v2</li>";
    echo "<li>أضف النطاق: <strong>" . $_SERVER['HTTP_HOST'] . "</strong></li>";
    echo "<li>انسخ Site Key و Secret Key</li>";
    echo "<li>اذهب إلى <a href='admin/settings.php' target='_blank'>إعدادات الموقع</a></li>";
    echo "<li>أدخل المفاتيح وفعل reCAPTCHA</li>";
    echo "</ol>";
    
    echo "<hr>";
    echo "<h3>اختبار سريع:</h3>";
    
    // اختبار الدوال
    include 'includes/recaptcha.php';
    $recaptcha_settings = getRecaptchaSettings($pdo);
    
    echo "<p><strong>حالة التفعيل:</strong> " . ($recaptcha_settings['enabled'] ? '✅ مفعل' : '❌ غير مفعل') . "</p>";
    echo "<p><strong>Site Key:</strong> " . ($recaptcha_settings['site_key'] ? '✅ موجود' : '❌ غير موجود') . "</p>";
    echo "<p><strong>Secret Key:</strong> " . ($recaptcha_settings['secret_key'] ? '✅ موجود' : '❌ غير موجود') . "</p>";
    
    if ($recaptcha_settings['enabled'] && $recaptcha_settings['site_key']) {
        echo "<h4>معاينة reCAPTCHA:</h4>";
        echo getRecaptchaScript(true);
        echo "<div style='border: 2px dashed #ccc; padding: 20px; margin: 10px 0; text-align: center;'>";
        echo renderRecaptcha($recaptcha_settings['site_key'], true);
        echo "</div>";
        echo "<p><em>إذا لم تظهر reCAPTCHA أعلاه، تأكد من صحة Site Key والنطاق المسجل</em></p>";
    }

    echo "<hr>";
    echo "<h3>تشخيص المشاكل الشائعة:</h3>";
    echo "<ul>";
    echo "<li><strong>لا تظهر reCAPTCHA:</strong> تأكد من تفعيلها في الإعدادات وصحة Site Key</li>";
    echo "<li><strong>خطأ في التحقق:</strong> تأكد من صحة Secret Key</li>";
    echo "<li><strong>خطأ النطاق:</strong> تأكد من إضافة " . $_SERVER['HTTP_HOST'] . " في إعدادات Google</li>";
    echo "<li><strong>لا تعمل محلياً:</strong> أضف localhost في إعدادات Google أيضاً</li>";
    echo "</ul>";

    echo "<h4>اختبار النماذج:</h4>";
    echo "<p>النماذج التي تحتوي على reCAPTCHA:</p>";
    echo "<ul>";
    echo "<li><a href='contact.php' target='_blank'>نموذج الاتصال</a></li>";
    echo "<li><a href='admin/login.php' target='_blank'>تسجيل دخول الإدارة</a></li>";
    echo "</ul>";
    
} catch(PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}

echo "<br><br>";
echo "<a href='admin/settings.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب للإعدادات</a> ";
echo "<a href='contact.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>اختبار النموذج</a>";
?>

<style>
body { font-family: 'Tajawal', Arial, sans-serif; direction: rtl; padding: 20px; }
h2, h3, h4 { color: #333; }
</style>
