<?php
include 'config.php';

echo "<h2>إصلاح إعدادات reCAPTCHA</h2>";

try {
    // تفعيل reCAPTCHA
    $stmt = $pdo->prepare("UPDATE settings SET setting_value = '1' WHERE setting_key = 'recaptcha_enabled'");
    $result = $stmt->execute();
    
    if ($result) {
        echo "<p style='color: green;'>✅ تم تفعيل reCAPTCHA بنجاح!</p>";
    } else {
        echo "<p style='color: red;'>❌ فشل في تفعيل reCAPTCHA</p>";
    }
    
    // التحقق من النتيجة
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE 'recaptcha_%'");
    $settings = $stmt->fetchAll();
    
    echo "<h3>الإعدادات بعد التحديث:</h3>";
    echo "<ul>";
    foreach ($settings as $setting) {
        $value = $setting['setting_value'];
        if ($setting['setting_key'] === 'recaptcha_secret_key' && !empty($value)) {
            $value = str_repeat('*', strlen($value));
        }
        
        $status = '';
        if ($setting['setting_key'] === 'recaptcha_enabled') {
            $status = $value == '1' ? ' ✅ مفعل' : ' ❌ غير مفعل';
        }
        
        echo "<li><strong>{$setting['setting_key']}:</strong> " . ($value ?: 'فارغ') . $status . "</li>";
    }
    echo "</ul>";
    
    // اختبار النظام
    include 'includes/recaptcha.php';
    $recaptcha_settings = getRecaptchaSettings($pdo);
    
    echo "<hr>";
    echo "<h3>اختبار النظام:</h3>";
    echo "<p><strong>حالة التفعيل:</strong> " . ($recaptcha_settings['enabled'] ? '✅ مفعل' : '❌ غير مفعل') . "</p>";
    echo "<p><strong>Site Key:</strong> " . ($recaptcha_settings['site_key'] ? '✅ موجود' : '❌ غير موجود') . "</p>";
    echo "<p><strong>Secret Key:</strong> " . ($recaptcha_settings['secret_key'] ? '✅ موجود' : '❌ غير موجود') . "</p>";
    
    if ($recaptcha_settings['enabled'] && $recaptcha_settings['site_key']) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>🎉 reCAPTCHA جاهز للعمل!</h4>";
        echo "<p style='margin: 0; color: #155724;'>يمكنك الآن اختبار النماذج والتأكد من ظهور reCAPTCHA</p>";
        echo "</div>";
        
        echo "<h4>معاينة reCAPTCHA:</h4>";
        echo getRecaptchaScript(true);
        echo "<div style='border: 2px dashed #28a745; padding: 20px; margin: 10px 0; text-align: center; background: #f8f9fa;'>";
        echo renderRecaptcha($recaptcha_settings['site_key'], true);
        echo "</div>";
    }
    
} catch(PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>الخطوات التالية:</h3>";
echo "<ol>";
echo "<li>اختبر نموذج الاتصال: <a href='contact.php' target='_blank' style='color: #007bff;'>contact.php</a></li>";
echo "<li>اختبر تسجيل دخول الإدارة: <a href='admin/login.php' target='_blank' style='color: #007bff;'>admin/login.php</a></li>";
echo "<li>إذا لم تظهر reCAPTCHA، تأكد من إضافة النطاق في Google reCAPTCHA</li>";
echo "</ol>";

echo "<br><br>";
echo "<a href='contact.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>اختبار النموذج الآن</a> ";
echo "<a href='admin/settings.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>إعدادات الموقع</a>";
?>

<style>
body { font-family: 'Tajawal', Arial, sans-serif; direction: rtl; padding: 20px; max-width: 800px; margin: 0 auto; }
h2, h3, h4 { color: #333; }
ul, ol { text-align: right; }
</style>
