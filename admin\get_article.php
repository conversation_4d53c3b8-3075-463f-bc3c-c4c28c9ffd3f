<?php
session_start();
include '../config.php';

// تحديد نوع المحتوى
header('Content-Type: application/json; charset=utf-8');

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'غير مصرح']);
    exit;
}

$id = $_GET['id'] ?? 0;

if (!$id) {
    echo json_encode(['success' => false, 'message' => 'معرف المقال مطلوب']);
    exit;
}

try {
    $stmt = $pdo->prepare("SELECT * FROM articles WHERE id = ?");
    $stmt->execute([$id]);
    $article = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($article) {
        echo json_encode([
            'success' => true,
            'article' => $article
        ], JSON_UNESCAPED_UNICODE);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'المقال غير موجود',
            'debug' => 'Article ID: ' . $id
        ], JSON_UNESCAPED_UNICODE);
    }
} catch(PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage(),
        'debug' => 'PDO Error'
    ], JSON_UNESCAPED_UNICODE);
}
?>
