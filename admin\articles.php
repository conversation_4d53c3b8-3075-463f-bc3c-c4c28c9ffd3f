<?php
session_start();
include '../config.php';

if (!isLoggedIn()) {
    redirectToLogin();
}

$message = '';
$error = '';

// إنشاء جدول المقالات إذا لم يكن موجوداً
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS articles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        excerpt TEXT,
        content LONGTEXT NOT NULL,
        image VARCHAR(255),
        author VARCHAR(100) DEFAULT 'فريق نقرة',
        status ENUM('published', 'draft') DEFAULT 'published',
        featured BOOLEAN DEFAULT FALSE,
        meta_title VARCHAR(255),
        meta_description TEXT,
        tags TEXT,
        views INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
} catch(PDOException $e) {
    $error = 'خطأ في إنشاء جدول المقالات: ' . $e->getMessage();
}

// معالجة الإجراءات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $title = cleanInput($_POST['title']);
                $excerpt = cleanInput($_POST['excerpt']);
                $content = cleanInput($_POST['content']);
                $author = cleanInput($_POST['author']) ?: 'فريق نقرة';
                $status = $_POST['status'] ?? 'published';
                $featured = isset($_POST['featured']) ? 1 : 0;
                $meta_title = cleanInput($_POST['meta_title']);
                $meta_description = cleanInput($_POST['meta_description']);
                $tags = cleanInput($_POST['tags']);
                $image = '';

                // التحقق من الصورة المختارة أولاً
                if (!empty($_POST['selected_image'])) {
                    $image = cleanInput($_POST['selected_image']);
                }
                // إذا لم تكن هناك صورة مختارة، تحقق من رفع صورة جديدة
                elseif (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                    $uploadDir = '../assets/images/articles/';
                    if (!file_exists($uploadDir)) {
                        mkdir($uploadDir, 0755, true);
                    }

                    $fileExtension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
                    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

                    if (in_array($fileExtension, $allowedExtensions)) {
                        if ($_FILES['image']['size'] <= 5 * 1024 * 1024) {
                            $fileName = 'article_' . time() . '_' . rand(1000, 9999) . '.' . $fileExtension;
                            $uploadPath = $uploadDir . $fileName;

                            if (move_uploaded_file($_FILES['image']['tmp_name'], $uploadPath)) {
                                $image = 'assets/images/articles/' . $fileName;
                            } else {
                                $error = 'فشل في رفع الصورة';
                            }
                        } else {
                            $error = 'حجم الصورة كبير جداً (الحد الأقصى 5MB)';
                        }
                    } else {
                        $error = 'نوع الملف غير مدعوم. استخدم: jpg, jpeg, png, gif, webp';
                    }
                }

                // إنشاء slug تلقائياً إذا لم يتم إدخاله
                $slug = !empty($_POST['slug']) ? cleanInput($_POST['slug']) :
                        strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));

                if (!empty($title) && !empty($content)) {
                    try {
                        $stmt = $pdo->prepare("INSERT INTO articles (title, slug, excerpt, content, image, author, status, featured, meta_title, meta_description, tags) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                        $stmt->execute([$title, $slug, $excerpt, $content, $image, $author, $status, $featured, $meta_title, $meta_description, $tags]);
                        $message = 'تم إضافة المقال بنجاح';
                    } catch(PDOException $e) {
                        $error = 'حدث خطأ أثناء إضافة المقال';
                    }
                } else {
                    $error = 'العنوان والمحتوى مطلوبان';
                }
                break;
            case 'edit':
                $id = (int)$_POST['id'];
                $title = cleanInput($_POST['title']);
                $excerpt = cleanInput($_POST['excerpt']);
                $content = cleanInput($_POST['content']);
                $author = cleanInput($_POST['author']) ?: 'فريق نقرة';
                $status = $_POST['status'] ?? 'published';
                $featured = isset($_POST['featured']) ? 1 : 0;
                $meta_title = cleanInput($_POST['meta_title']);
                $meta_description = cleanInput($_POST['meta_description']);
                $tags = cleanInput($_POST['tags']);
                $image = $_POST['current_image'] ?? '';

                // التحقق من الصورة المختارة أولاً
                if (!empty($_POST['selected_image'])) {
                    $image = cleanInput($_POST['selected_image']);
                }
                // إذا لم تكن هناك صورة مختارة، تحقق من رفع صورة جديدة
                elseif (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                    $uploadDir = '../assets/images/articles/';
                    if (!file_exists($uploadDir)) {
                        mkdir($uploadDir, 0755, true);
                    }

                    $fileExtension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
                    $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

                    if (in_array($fileExtension, $allowedExtensions)) {
                        if ($_FILES['image']['size'] <= 5 * 1024 * 1024) {
                            // حذف الصورة القديمة إذا كانت مختلفة
                            $oldImage = $_POST['current_image'] ?? '';
                            if (!empty($oldImage) && file_exists('../' . $oldImage)) {
                                unlink('../' . $oldImage);
                            }

                            $fileName = 'article_' . time() . '_' . rand(1000, 9999) . '.' . $fileExtension;
                            $uploadPath = $uploadDir . $fileName;

                            if (move_uploaded_file($_FILES['image']['tmp_name'], $uploadPath)) {
                                $image = 'assets/images/articles/' . $fileName;
                            } else {
                                $error = 'فشل في رفع الصورة';
                            }
                        } else {
                            $error = 'حجم الصورة كبير جداً (الحد الأقصى 5MB)';
                        }
                    } else {
                        $error = 'نوع الملف غير مدعوم. استخدم: jpg, jpeg, png, gif, webp';
                    }
                }

                // إنشاء slug تلقائياً إذا لم يتم إدخاله
                $slug = !empty($_POST['slug']) ? cleanInput($_POST['slug']) :
                        strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $title)));

                if (!empty($title) && !empty($content)) {
                    try {
                        $stmt = $pdo->prepare("UPDATE articles SET title = ?, slug = ?, excerpt = ?, content = ?, image = ?, author = ?, status = ?, featured = ?, meta_title = ?, meta_description = ?, tags = ? WHERE id = ?");
                        $stmt->execute([$title, $slug, $excerpt, $content, $image, $author, $status, $featured, $meta_title, $meta_description, $tags, $id]);
                        $message = 'تم تحديث المقال بنجاح';
                    } catch(PDOException $e) {
                        $error = 'حدث خطأ أثناء تحديث المقال';
                    }
                } else {
                    $error = 'العنوان والمحتوى مطلوبان';
                }
                break;

            case 'delete':
                $id = (int)$_POST['id'];
                try {
                    // جلب معلومات المقال لحذف الصورة
                    $stmt = $pdo->prepare("SELECT image FROM articles WHERE id = ?");
                    $stmt->execute([$id]);
                    $article = $stmt->fetch();

                    // حذف الصورة إذا كانت موجودة
                    if ($article && !empty($article['image']) && file_exists('../' . $article['image'])) {
                        unlink('../' . $article['image']);
                    }

                    // حذف المقال من قاعدة البيانات
                    $stmt = $pdo->prepare("DELETE FROM articles WHERE id = ?");
                    $stmt->execute([$id]);
                    $message = 'تم حذف المقال بنجاح';
                } catch(PDOException $e) {
                    $error = 'حدث خطأ أثناء حذف المقال';
                }
                break;
        }
    }
}

// جلب المقالات
try {
    $stmt = $pdo->query("SELECT * FROM articles ORDER BY created_at DESC");
    $articles = $stmt->fetchAll();
} catch(PDOException $e) {
    $articles = [];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المقالات - لوحة التحكم</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/logo2.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/logo2.png">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">

    <?php include 'includes/admin-styles.php'; ?>

    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #2563eb;
            --accent-color: #f59e0b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --text-color: #1f2937;
            --light-bg: #f8fafc;
            --white: #ffffff;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            --gradient-accent: linear-gradient(135deg, var(--accent-color), #d97706);
            --gradient-success: linear-gradient(135deg, var(--success-color), #059669);
            --gradient-danger: linear-gradient(135deg, var(--danger-color), #dc2626);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            color: var(--text-color);
            min-height: 100vh;
        }

        /* Sidebar Styles */
        .sidebar {
            background: var(--gradient-primary);
            min-height: 100vh;
            width: 280px;
            position: fixed;
            top: 0;
            right: 0;
            z-index: 1000;
            padding: 0;
            box-shadow: var(--shadow-lg);
            backdrop-filter: blur(10px);
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .sidebar-brand {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            background: rgba(255, 255, 255, 0.9);
            padding: 1rem;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar-brand img {
            width: 50px;
            height: 50px;
            margin-left: 15px;
        }

        .sidebar-brand h4 {
            color: var(--primary-color);
            font-weight: 800;
            font-size: 1.2rem;
            margin: 0;
        }

        .admin-info {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .sidebar nav {
            padding: 1rem 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 4px solid transparent;
            position: relative;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            transform: scaleX(0);
            transform-origin: right;
            transition: transform 0.3s ease;
        }

        .sidebar a:hover::before,
        .sidebar a.active::before {
            transform: scaleX(1);
        }

        .sidebar a:hover,
        .sidebar a.active {
            color: white;
            border-right-color: var(--accent-color);
        }

        .sidebar a i {
            margin-left: 15px;
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
            position: relative;
            z-index: 2;
        }

        .sidebar a span {
            position: relative;
            z-index: 2;
            font-weight: 500;
        }

        /* Main Content */
        .main-content {
            margin-right: 280px;
            padding: 2rem;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            padding: 1.5rem 2rem;
            border-radius: 20px;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .page-header h2 {
            margin: 0;
            color: var(--text-color);
            font-weight: 800;
            font-size: 1.8rem;
        }

        /* Cards */
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .card-header {
            background: var(--gradient-primary);
            color: white;
            border-radius: 20px 20px 0 0 !important;
            padding: 1.5rem;
            border: none;
        }

        .card-header h5 {
            margin: 0;
            font-weight: 700;
        }

        .card-body {
            padding: 2rem;
        }

        /* Buttons */
        .btn {
            border-radius: 50px;
            font-weight: 600;
            padding: 0.5rem 1.5rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Alerts */
        .alert {
            border-radius: 15px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }

        /* Forms */
        .form-control {
            border-radius: 10px;
            border: 2px solid #e5e7eb;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* Tables */
        .table {
            border-radius: 15px;
            overflow: hidden;
        }

        .table th {
            background: var(--light-bg);
            border: none;
            font-weight: 700;
            color: var(--text-color);
        }

        .table td {
            border: none;
            vertical-align: middle;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                transition: transform 0.3s ease;
                width: 100%;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
                padding: 1rem;
            }

            .page-header {
                padding: 1rem;
            }

            .page-header h2 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <div class="page-header">
            <div style="display: flex; align-items: center;">
                <img src="../assets/images/logo2.png" alt="Logo" style="width: 40px; height: 40px; margin-left: 15px;">
                <h2>إدارة المقالات</h2>
            </div>
        </div>
                
        <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <?php echo $message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>
        
        <!-- إضافة مقال جديد -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-plus me-2"></i>إضافة مقال جديد</h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="add">
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="title" class="form-label">عنوان المقال *</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="author" class="form-label">الكاتب</label>
                            <input type="text" class="form-control" id="author" name="author" value="فريق نقرة">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="slug" class="form-label">الرابط (اختياري)</label>
                            <input type="text" class="form-control" id="slug" name="slug" placeholder="سيتم إنشاؤه تلقائياً">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-control" id="status" name="status">
                                <option value="published">منشور</option>
                                <option value="draft">مسودة</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="image" class="form-label">صورة المقال</label>
                        <div class="row">
                            <div class="col-md-6">
                                <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                <div class="form-text">رفع صورة جديدة</div>
                            </div>
                            <div class="col-md-6">
                                <button type="button" class="btn btn-outline-primary w-100" onclick="openImageSelector('add')">
                                    <i class="fas fa-images me-2"></i>اختر من الصور الموجودة
                                </button>
                            </div>
                        </div>
                        <input type="hidden" id="selected_image" name="selected_image">
                        <div id="selected_image_preview" class="mt-2"></div>
                    </div>
                    <div class="mb-3">
                        <label for="excerpt" class="form-label">المقدمة</label>
                        <textarea class="form-control" id="excerpt" name="excerpt" rows="3" placeholder="مقدمة قصيرة عن المقال"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="content" class="form-label">محتوى المقال *</label>
                        <textarea class="form-control" id="content" name="content" rows="10" required></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="meta_title" class="form-label">عنوان SEO</label>
                            <input type="text" class="form-control" id="meta_title" name="meta_title">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="tags" class="form-label">الكلمات المفتاحية</label>
                            <input type="text" class="form-control" id="tags" name="tags" placeholder="مفصولة بفواصل">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="meta_description" class="form-label">وصف SEO</label>
                        <textarea class="form-control" id="meta_description" name="meta_description" rows="2"></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="featured" name="featured">
                            <label class="form-check-label" for="featured">
                                مقال مميز
                            </label>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>إضافة المقال
                    </button>
                </form>
            </div>
        </div>
        
        <!-- قائمة المقالات -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list me-2"></i>المقالات الموجودة</h5>
            </div>
            <div class="card-body">
                <?php if ($articles): ?>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>العنوان</th>
                                <th>الكاتب</th>
                                <th>الحالة</th>
                                <th>المشاهدات</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($articles as $article): ?>
                            <tr>
                                <td>
                                    <?php if (!empty($article['image'])): ?>
                                        <img src="../<?php echo htmlspecialchars($article['image']); ?>"
                                             alt="<?php echo htmlspecialchars($article['title']); ?>"
                                             style="width: 60px; height: 60px; object-fit: cover; border-radius: 8px;">
                                    <?php else: ?>
                                        <div style="width: 60px; height: 60px; background: #f3f4f6; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($article['title']); ?></strong>
                                    <?php if ($article['featured']): ?>
                                    <span class="badge bg-warning ms-2">مميز</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo htmlspecialchars($article['author']); ?></td>
                                <td>
                                    <?php if ($article['status'] == 'published'): ?>
                                    <span class="badge bg-success">منشور</span>
                                    <?php else: ?>
                                    <span class="badge bg-secondary">مسودة</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo number_format($article['views']); ?></td>
                                <td><?php echo date('Y-m-d', strtotime($article['created_at'])); ?></td>
                                <td>
                                    <?php if ($article['status'] == 'published'): ?>
                                    <a href="../article.php?slug=<?php echo $article['slug']; ?>" target="_blank" class="btn btn-sm btn-success" title="عرض في الموقع">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php endif; ?>

                                    <button type="button" class="btn btn-sm btn-primary" onclick="editArticle(<?php echo $article['id']; ?>)" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </button>

                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="toggle_status">
                                        <input type="hidden" name="id" value="<?php echo $article['id']; ?>">
                                        <button type="submit" class="btn btn-sm btn-outline-primary" title="<?php echo $article['status'] == 'published' ? 'إخفاء المقال' : 'نشر المقال'; ?>">
                                            <?php echo $article['status'] == 'published' ? 'إخفاء' : 'نشر'; ?>
                                        </button>
                                    </form>

                                    <form method="POST" style="display: inline;" onsubmit="return confirm('هل أنت متأكد من حذف هذا المقال؟')">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="id" value="<?php echo $article['id']; ?>">
                                        <button type="submit" class="btn btn-sm btn-danger" title="حذف المقال">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد مقالات حالياً</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Modal للتعديل -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل المقال</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data" id="editForm">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="id" id="editId">
                    <div class="modal-body">
                        <input type="hidden" name="current_image" id="currentImagePath">
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="editTitle" class="form-label">عنوان المقال</label>
                                <input type="text" class="form-control" id="editTitle" name="title" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="editStatus" class="form-label">الحالة</label>
                                <select class="form-control" id="editStatus" name="status">
                                    <option value="published">منشور</option>
                                    <option value="draft">مسودة</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-8 mb-3">
                                <label for="editSlug" class="form-label">الرابط (اختياري)</label>
                                <input type="text" class="form-control" id="editSlug" name="slug">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="editAuthor" class="form-label">الكاتب</label>
                                <input type="text" class="form-control" id="editAuthor" name="author" value="فريق نقرة">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="editImage" class="form-label">صورة المقال</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="file" class="form-control" id="editImage" name="image" accept="image/*">
                                    <div class="form-text">رفع صورة جديدة</div>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-outline-primary w-100" onclick="openImageSelector('edit')">
                                        <i class="fas fa-images me-2"></i>اختر من الصور الموجودة
                                    </button>
                                </div>
                            </div>
                            <input type="hidden" id="edit_selected_image" name="selected_image">
                            <div id="currentImage"></div>
                            <div id="edit_selected_image_preview" class="mt-2"></div>
                        </div>
                        <div class="mb-3">
                            <label for="editExcerpt" class="form-label">المقدمة</label>
                            <textarea class="form-control" id="editExcerpt" name="excerpt" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="editContent" class="form-label">المحتوى</label>
                            <textarea class="form-control" id="editContent" name="content" rows="8" required></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editMetaTitle" class="form-label">عنوان SEO</label>
                                <input type="text" class="form-control" id="editMetaTitle" name="meta_title">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editTags" class="form-label">الكلمات المفتاحية</label>
                                <input type="text" class="form-control" id="editTags" name="tags">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="editMetaDescription" class="form-label">وصف SEO</label>
                            <textarea class="form-control" id="editMetaDescription" name="meta_description" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="editFeatured" name="featured">
                                <label class="form-check-label" for="editFeatured">مقال مميز</label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Image Selector Modal -->
    <div class="modal fade" id="imageSelectorModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">اختر صورة من الصور الموجودة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row" id="imageGrid">
                        <div class="col-12 text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="selectImageBtn" disabled>اختيار الصورة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // تهيئة التنبيهات
    document.addEventListener('DOMContentLoaded', function() {
        // إخفاء التنبيهات بعد 5 ثواني
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // تأكيد الحذف
        const deleteForms = document.querySelectorAll('form[onsubmit*="confirm"]');
        deleteForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                if (!confirm('هل أنت متأكد من حذف هذا المقال؟')) {
                    e.preventDefault();
                }
            });
        });

        // تأكيد تغيير الحالة
        const statusForms = document.querySelectorAll('form[action=""][name="action"][value="toggle_status"]');
        statusForms.forEach(form => {
            form.addEventListener('submit', function() {
                // إضافة تأثير تحميل للزر
                const button = this.querySelector('button');
                button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';
                button.disabled = true;
            });
        });
    });

    function editArticle(id) {
        // جلب بيانات المقال
        fetch('get_article.php?id=' + id)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('editId').value = data.article.id;
                    document.getElementById('editTitle').value = data.article.title;
                    document.getElementById('editSlug').value = data.article.slug || '';
                    document.getElementById('editStatus').value = data.article.status;
                    document.getElementById('editExcerpt').value = data.article.excerpt || '';
                    document.getElementById('editContent').value = data.article.content;
                    document.getElementById('editAuthor').value = data.article.author;
                    document.getElementById('editMetaTitle').value = data.article.meta_title || '';
                    document.getElementById('editMetaDescription').value = data.article.meta_description || '';
                    document.getElementById('editTags').value = data.article.tags || '';
                    document.getElementById('editFeatured').checked = data.article.featured == 1;
                    document.getElementById('currentImagePath').value = data.article.image || '';

                    // عرض الصورة الحالية
                    const currentImageDiv = document.getElementById('currentImage');
                    if (data.article.image) {
                        currentImageDiv.innerHTML = '<div class="mt-2"><small>الصورة الحالية:</small><br><img src="../' + data.article.image + '" style="max-width: 100px; height: auto; border-radius: 5px;"></div>';
                    } else {
                        currentImageDiv.innerHTML = '';
                    }

                    // مسح الصورة المختارة السابقة
                    document.getElementById('edit_selected_image').value = '';
                    document.getElementById('edit_selected_image_preview').innerHTML = '';

                    // فتح الـ modal
                    new bootstrap.Modal(document.getElementById('editModal')).show();
                } else {
                    showAlert('خطأ في جلب بيانات المقال', 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('خطأ في الاتصال بالخادم', 'danger');
            });
    }

    // دالة لعرض تنبيه
    function showAlert(message, type = 'success') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // إضافة التنبيه في أعلى المحتوى
        const mainContent = document.querySelector('.main-content');
        const pageHeader = document.querySelector('.page-header');
        mainContent.insertBefore(alertDiv, pageHeader.nextSibling);

        // إخفاء التنبيه بعد 5 ثواني
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alertDiv);
            bsAlert.close();
        }, 5000);
    }

    // نظام اختيار الصور
    let currentImageSelector = 'add';
    let selectedImagePath = '';

    function openImageSelector(type) {
        currentImageSelector = type;
        selectedImagePath = '';
        document.getElementById('selectImageBtn').disabled = true;

        // فتح الـ modal
        const modal = new bootstrap.Modal(document.getElementById('imageSelectorModal'));
        modal.show();

        // جلب الصور
        loadImages();
    }

    function loadImages() {
        fetch('get_images.php?type=articles')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayImages(data.images);
                } else {
                    document.getElementById('imageGrid').innerHTML = '<div class="col-12 text-center text-danger">خطأ في جلب الصور</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('imageGrid').innerHTML = '<div class="col-12 text-center text-danger">خطأ في الاتصال بالخادم</div>';
            });
    }

    function displayImages(images) {
        const imageGrid = document.getElementById('imageGrid');

        if (images.length === 0) {
            imageGrid.innerHTML = '<div class="col-12 text-center text-muted">لا توجد صور متاحة</div>';
            return;
        }

        let html = '';
        images.forEach(image => {
            html += `
                <div class="col-md-3 col-sm-4 col-6 mb-3">
                    <div class="image-item" onclick="selectImage('${image.path}', this)" style="cursor: pointer; border: 2px solid transparent; border-radius: 8px; padding: 5px; transition: all 0.3s ease;">
                        <img src="../${image.path}" alt="${image.name}" style="width: 100%; height: 120px; object-fit: cover; border-radius: 5px;">
                        <div class="text-center mt-1">
                            <small class="text-muted">${image.name}</small>
                        </div>
                    </div>
                </div>
            `;
        });

        imageGrid.innerHTML = html;
    }

    function selectImage(imagePath, element) {
        // إزالة التحديد من جميع الصور
        document.querySelectorAll('.image-item').forEach(item => {
            item.style.borderColor = 'transparent';
            item.style.backgroundColor = 'transparent';
        });

        // تحديد الصورة المختارة
        element.style.borderColor = '#0d6efd';
        element.style.backgroundColor = 'rgba(13, 110, 253, 0.1)';

        selectedImagePath = imagePath;
        document.getElementById('selectImageBtn').disabled = false;
    }

    // عند النقر على زر اختيار الصورة
    document.getElementById('selectImageBtn').addEventListener('click', function() {
        if (selectedImagePath) {
            if (currentImageSelector === 'add') {
                document.getElementById('selected_image').value = selectedImagePath;
                document.getElementById('selected_image_preview').innerHTML =
                    '<div class="mt-2"><small>الصورة المختارة:</small><br><img src="../' + selectedImagePath + '" style="max-width: 100px; height: auto; border-radius: 5px;"></div>';
            } else if (currentImageSelector === 'edit') {
                document.getElementById('edit_selected_image').value = selectedImagePath;
                document.getElementById('edit_selected_image_preview').innerHTML =
                    '<div class="mt-2"><small>الصورة المختارة:</small><br><img src="../' + selectedImagePath + '" style="max-width: 100px; height: auto; border-radius: 5px;"></div>';
            }

            // إغلاق الـ modal
            bootstrap.Modal.getInstance(document.getElementById('imageSelectorModal')).hide();
        }
    });
    </script>
</body>
</html>
