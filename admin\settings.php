<?php
session_start();
include '../config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$success_message = '';
$error_message = '';

// معالجة تحديث كلمة المرور
if ($_POST && isset($_POST['update_password'])) {
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';

    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $error_message = 'يرجى ملء جميع الحقول';
    } elseif ($new_password !== $confirm_password) {
        $error_message = 'كلمة المرور الجديدة وتأكيدها غير متطابقين';
    } elseif (strlen($new_password) < 6) {
        $error_message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } else {
        try {
            // التحقق من كلمة المرور الحالية
            $stmt = $pdo->prepare("SELECT password FROM admin WHERE username = ?");
            $stmt->execute([$_SESSION['admin_username']]);
            $admin = $stmt->fetch();

            if ($admin && password_verify($current_password, $admin['password'])) {
                // تحديث كلمة المرور
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("UPDATE admin SET password = ? WHERE username = ?");
                $stmt->execute([$hashed_password, $_SESSION['admin_username']]);

                $success_message = 'تم تحديث كلمة المرور بنجاح';
            } else {
                $error_message = 'كلمة المرور الحالية غير صحيحة';
            }
        } catch(PDOException $e) {
            $error_message = 'حدث خطأ أثناء تحديث كلمة المرور';
        }
    }
}

// معالجة تحديث ملف robots.txt
if ($_POST && isset($_POST['update_robots'])) {
    $robots_content = $_POST['robots_content'] ?? '';

    try {
        file_put_contents('../robots.txt', $robots_content);
        $success_message = 'تم تحديث ملف robots.txt بنجاح';
    } catch(Exception $e) {
        $error_message = 'حدث خطأ أثناء تحديث ملف robots.txt';
    }
}

// معالجة تحديث ملف sitemap.xml
if ($_POST && isset($_POST['update_sitemap'])) {
    $sitemap_content = $_POST['sitemap_content'] ?? '';

    try {
        file_put_contents('../sitemap.xml', $sitemap_content);
        $success_message = 'تم تحديث ملف sitemap.xml بنجاح';
    } catch(Exception $e) {
        $error_message = 'حدث خطأ أثناء تحديث ملف sitemap.xml';
    }
}

// معالجة تحديث إعدادات reCAPTCHA
if ($_POST && isset($_POST['update_recaptcha'])) {
    $recaptcha_site_key = trim($_POST['recaptcha_site_key'] ?? '');
    $recaptcha_secret_key = trim($_POST['recaptcha_secret_key'] ?? '');
    $recaptcha_enabled = isset($_POST['recaptcha_enabled']) ? 1 : 0;

    try {
        // إنشاء جدول الإعدادات إذا لم يكن موجوداً
        $pdo->exec("CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) UNIQUE NOT NULL,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        // حفظ إعدادات reCAPTCHA
        $recaptcha_settings = [
            'recaptcha_site_key' => $recaptcha_site_key,
            'recaptcha_secret_key' => $recaptcha_secret_key,
            'recaptcha_enabled' => $recaptcha_enabled
        ];

        foreach ($recaptcha_settings as $key => $value) {
            $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = ?");
            $stmt->execute([$key, $value, $value]);
        }

        $success_message = 'تم حفظ إعدادات reCAPTCHA بنجاح';
    } catch(PDOException $e) {
        $error_message = 'خطأ في حفظ إعدادات reCAPTCHA: ' . $e->getMessage();
    }
}

// قراءة الإعدادات من قاعدة البيانات
$recaptcha_site_key = '';
$recaptcha_secret_key = '';
$recaptcha_enabled = false;

try {
    $stmt = $pdo->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('recaptcha_site_key', 'recaptcha_secret_key', 'recaptcha_enabled')");
    $settings = $stmt->fetchAll();

    foreach ($settings as $setting) {
        switch ($setting['setting_key']) {
            case 'recaptcha_site_key':
                $recaptcha_site_key = $setting['setting_value'];
                break;
            case 'recaptcha_secret_key':
                $recaptcha_secret_key = $setting['setting_value'];
                break;
            case 'recaptcha_enabled':
                $recaptcha_enabled = (bool)$setting['setting_value'];
                break;
        }
    }
} catch(PDOException $e) {
    // في حالة عدم وجود الجدول أو خطأ
}

// قراءة محتوى الملفات
$robots_content = file_exists('../robots.txt') ? file_get_contents('../robots.txt') : '';
$sitemap_content = file_exists('../sitemap.xml') ? file_get_contents('../sitemap.xml') : '';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإعدادات - شركة نقرة للتسويق الإلكتروني</title>

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../assets/images/logo2.png">
    <link rel="shortcut icon" type="image/png" href="../assets/images/logo2.png">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">

    <?php include 'includes/admin-styles.php'; ?>

    <style>


        .sidebar {
            background: var(--gradient-primary);
            min-height: 100vh;
            width: 280px;
            position: fixed;
            top: 0;
            right: 0;
            z-index: 1000;
            padding: 0;
            box-shadow: var(--shadow-lg);
            backdrop-filter: blur(10px);
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .sidebar-brand {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            background: rgba(255, 255, 255, 0.9);
            padding: 1rem;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .sidebar-brand img {
            width: 50px;
            height: 50px;
            margin-left: 15px;
        }

        .sidebar-brand h4 {
            color: var(--primary-color);
            font-weight: 800;
            font-size: 1.2rem;
            margin: 0;
        }

        .admin-info {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .sidebar nav {
            padding: 1rem 0;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 4px solid transparent;
            position: relative;
        }

        .sidebar a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            transform: scaleX(0);
            transform-origin: right;
            transition: transform 0.3s ease;
        }

        .sidebar a:hover::before,
        .sidebar a.active::before {
            transform: scaleX(1);
        }

        .sidebar a:hover,
        .sidebar a.active {
            color: white;
            border-right-color: var(--accent-color);
        }

        .sidebar a i {
            margin-left: 15px;
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
            position: relative;
            z-index: 2;
        }

        .sidebar a span {
            position: relative;
            z-index: 2;
            font-weight: 500;
        }

        .main-content {
            margin-right: 280px;
            padding: 2rem;
            min-height: 100vh;
        }

        .page-header {
            background: white;
            padding: 1.5rem 2rem;
            border-radius: 20px;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .page-header h2 {
            margin: 0;
            color: var(--text-color);
            font-weight: 800;
            font-size: 1.8rem;
        }

        .settings-card {
            background: white;
            border-radius: 20px;
            box-shadow: var(--shadow);
            overflow: hidden;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
        }

        .settings-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .settings-header {
            background: var(--gradient-primary);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .btn {
            border-radius: 50px;
            font-weight: 600;
            padding: 0.5rem 1.5rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .alert {
            border-radius: 15px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }

        .form-control {
            border-radius: 10px;
            border: 2px solid #e5e7eb;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        }

        .settings-header h1 {
            margin: 0;
            font-weight: 800;
            font-size: 1.8rem;
        }

        .settings-body {
            padding: 2rem;
        }

        .form-control {
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        }

        .btn-primary {
            background: var(--gradient-primary);
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: #6b7280;
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #4b5563;
            transform: translateY(-2px);
        }

        .alert {
            border-radius: 10px;
            padding: 1rem 1.5rem;
            margin-bottom: 2rem;
        }

        .settings-section {
            background: var(--light-bg);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .settings-section h3 {
            color: var(--text-color);
            font-weight: 700;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
        }

        .settings-section h3 i {
            margin-left: 10px;
            color: var(--primary-color);
        }

        .info-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
            margin-bottom: 1rem;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: var(--text-color);
        }

        .info-value {
            color: #6b7280;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content -->
    <div class="main-content">
        <div class="page-header">
            <div style="display: flex; align-items: center;">
                <img src="../assets/images/logo2.png" alt="Logo" style="width: 40px; height: 40px; margin-left: 15px;">
                <h2>إعدادات لوحة التحكم</h2>
            </div>
        </div>

        <!-- رسائل النجاح والخطأ -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

                <!-- معلومات الحساب -->
                <div class="settings-section">
                    <h3>
                        <i class="fas fa-user"></i>
                        معلومات الحساب
                    </h3>
                    <div class="info-card">
                        <div class="info-item">
                            <span class="info-label">اسم المستخدم:</span>
                            <span class="info-value"><?php echo htmlspecialchars($_SESSION['admin_username']); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">آخر تسجيل دخول:</span>
                            <span class="info-value"><?php echo date('Y-m-d H:i:s'); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">حالة الحساب:</span>
                            <span class="badge bg-success">نشط</span>
                        </div>
                    </div>
                </div>

                <!-- تغيير كلمة المرور -->
                <div class="settings-section">
                    <h3>
                        <i class="fas fa-lock"></i>
                        تغيير كلمة المرور
                    </h3>
                    <form method="POST">
                        <input type="hidden" name="update_password" value="1">
                        
                        <div class="mb-3">
                            <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور الجديدة</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required minlength="6">
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ التغييرات
                            </button>
                            <a href="dashboard.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-right me-2"></i>
                                العودة للوحة التحكم
                            </a>
                        </div>
                    </form>
                </div>

                <!-- إعدادات الموقع -->
                <div class="settings-section">
                    <h3>
                        <i class="fas fa-cog"></i>
                        إعدادات الموقع
                    </h3>

                    <!-- تحديث robots.txt -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">ملف robots.txt</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="update_robots" value="1">
                                <div class="mb-3">
                                    <label for="robots_content" class="form-label">محتوى ملف robots.txt</label>
                                    <textarea class="form-control" id="robots_content" name="robots_content" rows="8" style="font-family: monospace;"><?php echo htmlspecialchars($robots_content); ?></textarea>
                                    <div class="form-text">يتحكم هذا الملف في كيفية أرشفة محركات البحث لموقعك</div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ robots.txt
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- تحديث sitemap.xml -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">ملف sitemap.xml</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="update_sitemap" value="1">
                                <div class="mb-3">
                                    <label for="sitemap_content" class="form-label">محتوى ملف sitemap.xml</label>
                                    <textarea class="form-control" id="sitemap_content" name="sitemap_content" rows="12" style="font-family: monospace;"><?php echo htmlspecialchars($sitemap_content); ?></textarea>
                                    <div class="form-text">خريطة الموقع تساعد محركات البحث في فهرسة صفحات موقعك</div>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    حفظ sitemap.xml
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- إعدادات reCAPTCHA -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-shield-alt me-2"></i>
                                إعدادات Google reCAPTCHA
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>كيفية الحصول على مفاتيح reCAPTCHA:</strong><br>
                                1. اذهب إلى <a href="https://www.google.com/recaptcha/admin" target="_blank">Google reCAPTCHA</a><br>
                                2. أضف موقعك واختر reCAPTCHA v2<br>
                                3. انسخ Site Key و Secret Key وضعهما هنا
                            </div>

                            <?php if ($recaptcha_enabled && !empty($recaptcha_site_key) && !empty($recaptcha_secret_key)): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                <strong>reCAPTCHA مفعل ويعمل بشكل صحيح!</strong>
                                <br><small>النطاق المسجل: <?php echo $_SERVER['HTTP_HOST']; ?></small>
                            </div>
                            <?php elseif ($recaptcha_enabled): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>reCAPTCHA مفعل ولكن المفاتيح غير مكتملة!</strong>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-secondary">
                                <i class="fas fa-times-circle me-2"></i>
                                <strong>reCAPTCHA غير مفعل حالياً</strong>
                            </div>
                            <?php endif; ?>

                            <form method="POST">
                                <input type="hidden" name="update_recaptcha" value="1">

                                <div class="mb-3">
                                    <label for="recaptcha_site_key" class="form-label">Site Key (المفتاح العام)</label>
                                    <input type="text" class="form-control" id="recaptcha_site_key" name="recaptcha_site_key"
                                           value="<?php echo htmlspecialchars($recaptcha_site_key); ?>"
                                           placeholder="6Lc...">
                                    <div class="form-text">هذا المفتاح يظهر في الصفحات العامة</div>
                                </div>

                                <div class="mb-3">
                                    <label for="recaptcha_secret_key" class="form-label">Secret Key (المفتاح السري)</label>
                                    <input type="password" class="form-control" id="recaptcha_secret_key" name="recaptcha_secret_key"
                                           value="<?php echo htmlspecialchars($recaptcha_secret_key); ?>"
                                           placeholder="6Lc...">
                                    <div class="form-text">هذا المفتاح سري ولا يظهر للزوار</div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="recaptcha_enabled" name="recaptcha_enabled"
                                               <?php echo $recaptcha_enabled ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="recaptcha_enabled">
                                            تفعيل reCAPTCHA على النماذج
                                        </label>
                                    </div>
                                </div>

                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ إعدادات reCAPTCHA
                                    </button>

                                    <?php if ($recaptcha_enabled && !empty($recaptcha_site_key)): ?>
                                    <a href="../contact.php" target="_blank" class="btn btn-success">
                                        <i class="fas fa-test-tube me-2"></i>
                                        اختبار reCAPTCHA
                                    </a>
                                    <?php endif; ?>

                                    <a href="../setup_recaptcha.php" target="_blank" class="btn btn-info">
                                        <i class="fas fa-tools me-2"></i>
                                        أدوات التشخيص
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- معلومات النظام -->
                <div class="settings-section">
                    <h3>
                        <i class="fas fa-info-circle"></i>
                        معلومات النظام
                    </h3>
                    <div class="info-card">
                        <div class="info-item">
                            <span class="info-label">إصدار PHP:</span>
                            <span class="info-value"><?php echo phpversion(); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">الخادم:</span>
                            <span class="info-value"><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'غير محدد'; ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">نظام التشغيل:</span>
                            <span class="info-value"><?php echo PHP_OS; ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">حالة النظام:</span>
                            <span class="badge bg-success">يعمل بشكل طبيعي</span>
                        </div>
                    </div>
                </div>

                <!-- روابط سريعة -->
                <div class="settings-section">
                    <h3>
                        <i class="fas fa-external-link-alt"></i>
                        روابط سريعة
                    </h3>
                    <div class="d-flex flex-wrap gap-2">
                        <a href="dashboard.php" class="btn btn-outline-primary">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            لوحة التحكم
                        </a>
                        <a href="projects.php" class="btn btn-outline-success">
                            <i class="fas fa-project-diagram me-2"></i>
                            المشاريع
                        </a>
                        <a href="messages.php" class="btn btn-outline-info">
                            <i class="fas fa-envelope me-2"></i>
                            الرسائل
                        </a>
                        <a href="../index.php" target="_blank" class="btn btn-outline-warning">
                            <i class="fas fa-eye me-2"></i>
                            عرض الموقع
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
