<?php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'meedpsco_hostmeed');
define('DB_USER', 'meedpsco_hostmeed');
define('DB_PASS', 'meedpsco_hostmeed');

// إعدادات الموقع
define('SITE_URL', 'https://bag.meedps.com');
define('SITE_NAME', 'شركة نقرة للتسويق الإلكتروني');
define('COMPANY_PHONE', '01062751630');
define('COMPANY_EMAIL', '<EMAIL>');
define('COMPANY_ADDRESS', 'مصر - المنصورة - قرية نشا');
define('WHATSAPP_NUMBER', '2001062751630');

// الاتصال بقاعدة البيانات
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", 
        DB_USER, 
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
        ]
    );
} catch(PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات");
}

// وظائف مساعدة
function cleanInput($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

function isLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

function redirectToLogin() {
    header('Location: admin/login.php');
    exit;
}
?>
